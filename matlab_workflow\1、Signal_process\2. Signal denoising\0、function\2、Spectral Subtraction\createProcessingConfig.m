function config = createProcessingConfig()
%CREATEPROCESSINGCONFIG 创建信号处理参数配置结构体
%   集中管理所有信号处理相关的参数，包括采样率、滤波参数、谱减法参数等。
%   该函数提供了一个统一的配置管理接口，便于参数调整和维护。
%
%   语法:
%   config = createProcessingConfig()
%
%   输入参数:
%   无
%
%   输出参数:
%   config - 包含所有处理参数的结构体
%
%   配置参数说明:
%   采样参数:
%   - samplingRate: 目标采样率 (Hz)
%   
%   预处理参数:
%   - normalizationFactor: 归一化因子 (ADC量程相关)
%   - enableDetrend: 是否启用去趋势处理
%   - enableDCRemoval: 是否启用去直流分量
%   
%   滤波参数:
%   - filterLowFreq: 带通滤波器低频边界 (Hz)
%   - filterHighFreq: 带通滤波器高频边界 (Hz)
%   - filterOrder: 滤波器阶数 (可选)
%   
%   谱减法参数:
%   - spectralSubtractionIS: 初始静默段比例 (0-1)
%   - enableSpectralSubtraction: 是否启用谱减法降噪
%   - spectralSubtractionMethod: 谱减法方法选择
%   
%   输出参数:
%   - outputFolder: 输出文件夹名称
%   - filenameSuffix: 输出文件名后缀
%   - enableSNRCalculation: 是否计算SNR
%   
%   性能参数:
%   - enableProgressDisplay: 是否显示处理进度
%   - enableVerboseOutput: 是否启用详细输出
%
%   示例:
%   % 获取默认配置
%   config = createProcessingConfig();
%   
%   % 修改特定参数
%   config.samplingRate = 5000;
%   config.filterLowFreq = 50;
%   config.filterHighFreq = 1000;
%   
%   % 禁用谱减法
%   config.enableSpectralSubtraction = false;
%
%   注意事项:
%   - 修改参数前请确保了解其对信号处理的影响
%   - 滤波频率范围应在采样率的奈奎斯特频率内
%   - 静默段比例应根据信号特征调整
%   - 建议在修改配置后进行测试验证
%
%   参见: CSV_LVBO_TO_MAT_TT, PREPROCESSDATA, SSBOLL79
%
%   作者: [作者姓名]
%   日期: [创建日期]
%   版本: 1.0

    %% 基本采样参数
    config.samplingRate = 2570;                    % 采样率 (Hz)
    
    %% 预处理参数
    config.normalizationFactor = 8192;             % 归一化因子 (12位ADC半量程)
    config.enableDetrend = true;                   % 启用去趋势处理
    config.enableDCRemoval = true;                 % 启用去直流分量
    config.normalizationMethod = 'max_abs';        % 归一化方法: 'fixed_factor' 或 'max_abs'
    
    %% 带通滤波参数
    config.filterLowFreq = 100;                    % 带通滤波器低频边界 (Hz)
    config.filterHighFreq = 800;                   % 带通滤波器高频边界 (Hz)
    config.filterOrder = [];                       % 滤波器阶数 (空值使用默认)
    config.filterType = 'bandpass';                % 滤波器类型
    
    %% 谱减法降噪参数
    config.spectralSubtractionIS = 0.15;           % 初始静默段比例 (15%)
    config.enableSpectralSubtraction = true;       % 启用谱减法降噪
    config.spectralSubtractionMethod = 'SSBoll79'; % 谱减法方法: 'SSBoll79' 或 'SSBoll79m_2'
    config.spectralSubtractionAlpha = 0.12;        % SSBoll79m_2方法的alpha参数
    
    %% 输出控制参数
    config.outputFolder = '2、Processed data';     % 输出文件夹名称
    config.filenameSuffix = '_tt';                 % 输出文件名后缀
    config.enableSNRCalculation = true;            % 启用SNR计算
    config.saveIntermediateResults = false;        % 保存中间处理结果

    %% 二次分割参数
    config.enableSecondarySegmentation = true;     % 启用二次分割
    config.secondarySegmentLength = 60;            % 二次分割长度（秒）
    config.secondaryOverlapRatio = 0.0;            % 重叠比例（0-0.5）
    config.minSegmentLength = 5;                   % 最小片段长度（秒）
    
    %% 性能和显示参数
    config.enableProgressDisplay = true;           % 显示处理进度
    config.enableVerboseOutput = true;             % 启用详细输出信息
    config.enableErrorHandling = true;             % 启用错误处理
    
    %% 数据验证参数
    config.minSignalLength = 100;                  % 最小信号长度 (样本数)
    config.maxSignalLength = 1e7;                  % 最大信号长度 (样本数)
    config.requiredColumns = [2, 3];               % CSV文件必需的列索引
    
    %% 内存管理参数
    config.enableMemoryOptimization = true;        % 启用内存优化
    config.maxMemoryUsage = 1e9;                   % 最大内存使用量 (字节)
    
    %% 调试参数
    config.enableDebugMode = false;                % 启用调试模式
    config.saveDebugInfo = false;                  % 保存调试信息
    
    % 参数验证
    config = validateConfig(config);
end

function config = validateConfig(config)
%VALIDATECONFIG 验证配置参数的有效性
%   检查配置参数是否在合理范围内，并提供警告或错误信息

    % 验证采样率
    if config.samplingRate <= 0
        error('createProcessingConfig:InvalidSamplingRate', '采样率必须为正数');
    end
    
    % 验证滤波频率
    nyquistFreq = config.samplingRate / 2;
    if config.filterHighFreq >= nyquistFreq
        warning('createProcessingConfig:HighFreqTooHigh', ...
            '高频边界 (%.1f Hz) 接近奈奎斯特频率 (%.1f Hz)，建议调整', ...
            config.filterHighFreq, nyquistFreq);
        config.filterHighFreq = nyquistFreq * 0.9;
    end
    
    if config.filterLowFreq >= config.filterHighFreq
        error('createProcessingConfig:InvalidFilterRange', ...
            '低频边界必须小于高频边界');
    end
    
    % 验证静默段比例
    if config.spectralSubtractionIS <= 0 || config.spectralSubtractionIS >= 1
        error('createProcessingConfig:InvalidISRatio', ...
            '静默段比例必须在0和1之间');
    end
    
    % 验证归一化因子
    if config.normalizationFactor <= 0
        error('createProcessingConfig:InvalidNormalizationFactor', ...
            '归一化因子必须为正数');
    end
    
    % 显示配置摘要
    if config.enableVerboseOutput
        fprintf('=== 信号处理配置摘要 ===\n');
        fprintf('采样率: %d Hz\n', config.samplingRate);
        fprintf('带通滤波: %.1f - %.1f Hz\n', config.filterLowFreq, config.filterHighFreq);
        fprintf('谱减法IS: %.2f\n', config.spectralSubtractionIS);
        fprintf('输出文件夹: %s\n', config.outputFolder);
        fprintf('========================\n\n');
    end
end
