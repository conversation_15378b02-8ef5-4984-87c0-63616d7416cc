%TEST_VARIABLE_NAMING 测试修改后的变量命名功能
%   验证修改后的 csv_lvbo_to_mat_tt.m 能够正确生成包含原文件名前缀的变量名
%
%   功能:
%   1. 创建测试用的CSV文件
%   2. 模拟文件名清理和变量名生成过程
%   3. 验证生成的变量名格式是否正确
%
%   测试用例:
%   - 普通文件名: test_file.csv -> test_file_tt1, test_file_tt2
%   - 包含特殊字符: <EMAIL> -> test_file_123_tt1, test_file_123_tt2
%   - 数字开头: 123test.csv -> file_123test_tt1, file_123test_tt2

clear;
clc;

fprintf('=== 测试变量命名功能 ===\n\n');

%% 测试用例定义
testCases = {
    'test_file.csv', 'test_file_tt1', 'test_file_tt2';
    '<EMAIL>', 'signal_data_123_tt1', 'signal_data_123_tt2';
    '123test.csv', 'file_123test_tt1', 'file_123test_tt2';
    'data file with spaces.csv', 'data_file_with_spaces_tt1', 'data_file_with_spaces_tt2';
    'normal_file_name.csv', 'normal_file_name_tt1', 'normal_file_name_tt2'
};

%% 执行测试
allTestsPassed = true;

for i = 1:size(testCases, 1)
    fileName = testCases{i, 1};
    expectedVar1 = testCases{i, 2};
    expectedVar2 = testCases{i, 3};
    
    fprintf('测试用例 %d: %s\n', i, fileName);
    
    % 模拟文件名处理过程（与修改后的代码逻辑相同）
    [~, name, ~] = fileparts(fileName);
    
    % 清理文件名中的特殊字符，确保变量名有效
    cleanName = regexprep(name, '[^a-zA-Z0-9_]', '_');
    
    % 确保变量名以字母开头
    if ~isempty(cleanName) && ~isletter(cleanName(1))
        cleanName = ['file_', cleanName];
    end
    
    % 创建动态变量名
    var1Name = [cleanName, '_tt1'];
    var2Name = [cleanName, '_tt2'];
    
    % 验证结果
    if strcmp(var1Name, expectedVar1) && strcmp(var2Name, expectedVar2)
        fprintf('  ✓ 通过: %s -> %s, %s\n', name, var1Name, var2Name);
    else
        fprintf('  ✗ 失败: %s\n', name);
        fprintf('    期望: %s, %s\n', expectedVar1, expectedVar2);
        fprintf('    实际: %s, %s\n', var1Name, var2Name);
        allTestsPassed = false;
    end
    fprintf('\n');
end

%% 测试结果总结
fprintf('=== 测试结果 ===\n');
if allTestsPassed
    fprintf('✓ 所有测试用例通过！变量命名功能正常工作。\n');
else
    fprintf('✗ 部分测试用例失败，请检查代码逻辑。\n');
end

%% 额外验证：测试变量名的有效性
fprintf('\n=== 变量名有效性验证 ===\n');
testVarNames = {'test_file_tt1', 'signal_data_123_tt1', 'file_123test_tt1'};

for i = 1:length(testVarNames)
    varName = testVarNames{i};
    try
        % 尝试创建变量
        eval([varName, ' = 1;']);
        fprintf('✓ 变量名 "%s" 有效\n', varName);
        % 清理测试变量
        eval(['clear ', varName]);
    catch ME
        fprintf('✗ 变量名 "%s" 无效: %s\n', varName, ME.message);
    end
end

fprintf('\n测试完成。\n');
